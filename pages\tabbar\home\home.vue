<template>
  <view class="container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="avatar-container">
        <image class="avatar" :src="userInfo.avatar || '/static/user/avatar.jpg'"></image>
        <button class="edit-avatar-btn" @click="showAvatarPicker">更换头像</button>
      </view>

      <view class="user-info">
        <text class="nickname">{{ userInfo.nickname || '未设置昵称' }}</text>
        <text class="username">ID: {{ userInfo.id }}</text>
      </view>
    </view>

    <!-- 设置表单 -->
    <view class="settings-form">
      <view class="form-item">
        <text class="label">昵称</text>
        <input class="input" v-model="formData.nickname" placeholder="请输入昵称" />
      </view>

      <view class="form-item">
        <text class="label">性别</text>
        <picker class="picker" mode="selector" :range="genderOptions" @change="onGenderChange">
          <text>{{ formData.gender || '请选择性别' }}</text>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">生日</text>
        <picker class="picker" mode="date" @change="onBirthdayChange">
          <text>{{ formData.birthday || '请选择生日' }}</text>
        </picker>
      </view>

      <button class="save-btn" @click="saveSettings">保存设置</button>
      <button class="logout-btn" @click="logout">退出登录</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import request from '../../../api/request';

const baseURL = request.BASE_URL;
const userInfo = ref({});
const formData = ref({
  nickname: '',
  gender: '',
  birthday: ''
});
const genderOptions = ['男', '女', '其他'];

// 获取用户信息
const getUserInfo = async () => {
  try {
    const res = await uni.request({
      url: baseURL + 'user',
      method: 'GET',
      header: {
        'Cookie': `Authorization=${uni.getStorageSync('authorization')}`,
      },
      withCredentials: true
    });

    if (res.data && res.data.data) {
      userInfo.value = res.data.data;
      formData.value = {
		id:userInfo.value.id,
        nickname: userInfo.value.nickname,
        gender: userInfo.value.gender,
        birthday: userInfo.value.birthday
      };
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 保存设置（除头像外）
const saveSettings = async () => {
  try {
    const res = await uni.request({
      url: baseURL + 'user',
      method: 'PUT',
      header: {
        'Cookie': `Authorization=${uni.getStorageSync('authorization')}`,
        'Content-Type': 'application/json'
      },
      data: JSON.stringify(formData.value),
      withCredentials: true
    });

    if (res.data && res.data.code === 0) {
      uni.showToast({ title: '保存成功', icon: 'success' });
      getUserInfo();
    } else {
      const errorMsg = res.data?.message || '保存失败';
      console.error('保存设置失败:', errorMsg);
      uni.showToast({ title: errorMsg, icon: 'none' });
    }
  } catch (error) {
    console.error('保存设置失败:', error);
    uni.showToast({
      title: error.message || '保存失败',
      icon: 'none',
      duration: 2000
    });
  }
};

// 更新头像
const updateAvatar = (tempFilePath) => {
  return new Promise((resolve, reject) => {
    // 先将图片转为base64，然后通过PUT请求发送
    uni.getFileSystemManager().readFile({
      filePath: tempFilePath,
      encoding: 'base64',
      success: (res) => {
        const base64Data = res.data;

        // 构建请求数据
        const requestData = {
          avatar: `data:image/jpeg;base64,${base64Data}`,
          type: 'avatar'
        };

        // 发送PUT请求
        uni.request({
          url: baseURL + 'user',
          method: 'PUT',
          header: {
            'Cookie': `Authorization=${uni.getStorageSync('authorization')}`,
            'Content-Type': 'application/json'
          },
          data: requestData,
          success: (uploadRes) => {
            try {
              const responseData = uploadRes.data;

              if (responseData && responseData.code === 0) {
                uni.showToast({ title: '头像更新成功', icon: 'success' });
                getUserInfo();
                resolve(responseData);
              } else {
                const errorMsg = responseData?.message || '头像更新失败';
                uni.showToast({
                  title: errorMsg,
                  icon: 'none',
                  duration: 2000
                });
                reject(new Error(errorMsg));
              }
            } catch (parseError) {
              console.error('处理响应数据失败:', parseError);
              uni.showToast({
                title: '处理响应数据失败',
                icon: 'none',
                duration: 2000
              });
              reject(parseError);
            }
          },
          fail: (error) => {
            console.error('上传头像失败:', error);
            uni.showToast({
              title: '上传头像失败',
              icon: 'none',
              duration: 2000
            });
            reject(error);
          }
        });
      },
      fail: (error) => {
        console.error('读取图片失败:', error);
        uni.showToast({
          title: '读取图片失败',
          icon: 'none',
          duration: 2000
        });
        reject(error);
      }
    });
  });
};

// 退出登录
const logout = async () => {
  try {
    const res = await uni.request({
      url: baseURL + 'user',
      method: 'DELETE',
      header: {
        'Cookie': `Authorization=${uni.getStorageSync('authorization')}`
      },
      withCredentials: true
    });

    if (res.data && res.data.code === 0) {
      uni.removeStorageSync('authorization');
      uni.reLaunch({ url: '/pages/login/login' });
    } else {
      uni.showToast({ title: res.data.message || '退出失败', icon: 'none' });
    }
  } catch (error) {
    console.error('退出登录失败:', error);
    uni.showToast({ title: '退出失败', icon: 'none' });
  }
};

// 头像选择相关方法
const showAvatarPicker = () => {
  uni.showActionSheet({
    itemList: ['从相册选择', '拍照'],
    success: (res) => {
      if (res.tapIndex === 0) {
        chooseAvatar();
      } else if (res.tapIndex === 1) {
        takePhoto();
      }
    }
  });
};

const chooseAvatar = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album']
    });

    if (res.tempFilePaths.length > 0) {
      await updateAvatar(res.tempFilePaths[0]);
    }
  } catch (error) {
    console.error('选择头像失败:', error);
  }
};

const takePhoto = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera']
    });

    if (res.tempFilePaths.length > 0) {
      await updateAvatar(res.tempFilePaths[0]);
    }
  } catch (error) {
    console.error('拍照失败:', error);
  }
};

const onGenderChange = (e) => {
  formData.value.gender = genderOptions[e.detail.value];
};

const onBirthdayChange = (e) => {
  formData.value.birthday = e.detail.value;
};

onMounted(() => {
  getUserInfo();
});
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.user-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.avatar-container {
  position: relative;
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.edit-avatar-btn {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  height: 40rpx;
  line-height: 40rpx;
  border-radius: 0 0 60rpx 60rpx;
}

.user-info {
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.username {
  font-size: 24rpx;
  color: #999;
}

.settings-form {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.input {
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.picker {
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  line-height: 80rpx;
}

.save-btn {
  background-color: #07c160;
  color: #fff;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.logout-btn {
  background-color: #ff453a;
  color: #fff;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.avatar-popup {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx 16rpx 0 0;
}

.avatar-popup button {
  margin-bottom: 20rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  color: #333;
}

.avatar-popup button:last-child {
  margin-bottom: 0;
}
</style>