<template>
  <view class="container">
    <!-- 日记列表 -->
    <view class="daily-list" v-if="dailyList && dailyList.length > 0">
      <view class="daily-item" v-for="(item, index) in dailyList" :key="index">
        <view class="daily-header">
          <text class="daily-title">{{ item.title }}</text>
          <view class="daily-actions">
            <button class="action-btn edit" @click="editDaily(item)">编辑</button>
            <button class="action-btn delete" @click="deleteDaily(item.id)">删除</button>
          </view>
        </view>
        <view class="daily-content">{{ item.content }}</view>
        <image v-if="item.image" :src="item.image" class="daily-image" mode="aspectFill" @click="previewImage(item.image)" />
      </view>
    </view>
    
    <!-- 添加/编辑表单 -->
    <view class="form-container">
      <view class="form-header">
        <text class="form-title">{{ isEditing ? '编辑日记' : '新建日记' }}</text>
      </view>
      <input class="input-field" v-model="formData.title" placeholder="请输入标题" />
      <textarea class="textarea-field" v-model="formData.content" placeholder="写下你的想法..." />
      
      <!-- 图片上传区域 -->
      <view class="image-upload">
        <image v-if="formData.image" :src="formData.image" class="preview-image" mode="aspectFill" @click="previewImage(formData.image)" />
        <view v-else class="upload-placeholder" @click="chooseImage">
          <text class="iconfont icon-camera"></text>
          <text>点击添加图片</text>
        </view>
      </view>
      
      <view class="form-actions">
        <button class="submit-btn" @click="isEditing ? updateDaily() : addDaily()">
          {{ isEditing ? '更新' : '发布' }}
        </button>
        <button v-if="isEditing" class="cancel-btn" @click="cancelEdit">取消</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import request from '../../../api/request';

const baseURL = request.BASE_URL;
const app = getApp();
const authorization = uni.getStorageSync('authorization');

// 响应式数据
const dailyList = ref([]);
const isEditing = ref(false);
const formData = reactive({
  id: null,
  userId: null,
  title: '',
  content: '',
  image: ''
});

// 图片处理方法
const chooseImage = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      maxSize: 5 * 1024 * 1024 // 限制图片大小为5MB
    });
    
    if (!res || !res.tempFilePaths || !res.tempFilePaths.length) {
      throw new Error('选择图片失败');
    }
    
    const tempFilePath = res.tempFilePaths[0];
    const uploadRes = await uni.uploadFile({
      url: baseURL + 'daily/upload',
      filePath: tempFilePath,
      name: 'file',
      header: {
        'Authorization': authorization,
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000, // 设置60秒超时
      formData: {
        type: 'image'
      }
    });
    
    if (!uploadRes || !uploadRes.statusCode || uploadRes.statusCode !== 200) {
      throw new Error(`图片上传失败: ${uploadRes?.errMsg || '未知错误'}`);
    }
    
    try {
      const result = JSON.parse(uploadRes.data);
      if (result.code === 0 && result.data) {
        formData.image = result.data;
      } else {
        throw new Error(result.message || '图片上传失败');
      }
    } catch (parseError) {
      console.error('解析上传响应失败:', parseError);
      throw new Error('服务器响应格式错误');
    }
  } catch (error) {
    console.error('图片处理失败:', error);
    uni.showToast({
      title: error.message || '图片处理失败',
      icon: 'none',
      duration: 2000
    });
  }
};

const previewImage = (url) => {
  uni.previewImage({
    urls: [url]
  });
};

// API调用方法
const getDailyList = async () => {
  try {
    const res = await uni.request({
      url: baseURL + 'daily',
      method: 'GET',
      header: { 'Authorization': authorization },
      data: { userId: app.globalData.userId }
    });
    if (res.data.code === 0) {
      dailyList.value = res.data.data;
    } else {
      uni.showToast({
        title: res.data.message || '获取日记失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('获取日记列表失败:', error);
    uni.showToast({
      title: '获取日记列表失败',
      icon: 'none'
    });
  }
};

const addDaily = async () => {
  if (!formData.title || !formData.content) {
    uni.showToast({
      title: '请填写标题和内容',
      icon: 'none'
    });
    return;
  }
  
  try {
    formData.userId = app.globalData.userId;
    const res = await uni.request({
      url: baseURL + 'daily',
      method: 'POST',
      header: { 'Authorization': authorization },
      data: formData
    });
    
    if (res.data.code === 0) {
      uni.showToast({
        title: '发布成功',
        icon: 'success'
      });
      getDailyList();
      resetForm();
    } else {
      uni.showToast({
        title: res.data.message || '发布失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('添加日记失败:', error);
    uni.showToast({
      title: '发布失败',
      icon: 'none'
    });
  }
};

const updateDaily = async () => {
  if (!formData.title || !formData.content) {
    uni.showToast({
      title: '请填写标题和内容',
      icon: 'none'
    });
    return;
  }
  
  try {
    const res = await uni.request({
      url: baseURL + 'daily',
      method: 'PUT',
      header: { 'Authorization': authorization },
      data: formData
    });
    
    if (res.data.code === 0) {
      uni.showToast({
        title: '更新成功',
        icon: 'success'
      });
      getDailyList();
      resetForm();
      isEditing.value = false;
    } else {
      uni.showToast({
        title: res.data.message || '更新失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('更新日记失败:', error);
    uni.showToast({
      title: '更新失败',
      icon: 'none'
    });
  }
};

const deleteDaily = async (id) => {
  try {
    const [error, res] = await uni.showModal({
      title: '确认删除',
      content: '确定要删除这条日记吗？'
    });
    
    if (res.confirm) {
      const res = await uni.request({
        url: baseURL + 'daily',
        method: 'DELETE',
        header: { 'Authorization': authorization },
        data: { id, userId: app.globalData.userId }
      });
      
      if (res.data.code === 0) {
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
        getDailyList();
      } else {
        uni.showToast({
          title: res.data.message || '删除失败',
          icon: 'none'
        });
      }
    }
  } catch (error) {
    console.error('删除日记失败:', error);
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    });
  }
};

// 辅助方法
const editDaily = (daily) => {
  Object.assign(formData, daily);
  isEditing.value = true;
};

const cancelEdit = () => {
  resetForm();
  isEditing.value = false;
};

const resetForm = () => {
  formData.id = null;
  formData.title = '';
  formData.content = '';
  formData.image = '';
};

// 生命周期钩子
onLoad(() => {
  if (!app.checkLogin()) return;
  getDailyList();
});
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
}

.daily-list {
  margin-bottom: 30rpx;
}

.daily-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.daily-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.daily-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.daily-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  margin: 0;
}

.action-btn.edit {
  background-color: #4CAF50;
  color: white;
}

.action-btn.delete {
  background-color: #ff4444;
  color: white;
}

.daily-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin: 15rpx 0;
}

.daily-image {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
  margin-top: 15rpx;
}

.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-header {
  margin-bottom: 20rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.input-field {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.textarea-field {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.image-upload {
  margin: 20rpx 0;
}

.preview-image {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
}

.upload-placeholder {
  width: 100%;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
}

.icon-camera {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.submit-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4CAF50;
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
}
</style>