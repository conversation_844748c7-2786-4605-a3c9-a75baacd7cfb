da<template>
	<view class="message-container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<text class="title">消息中心</text>
		</view>
		
		<!-- 分类标签栏 -->
		<view class="tabs">
			<view 
				v-for="(tab, index) in tabs" 
				:key="index" 
				class="tab-item" 
				:class="{ active: currentTab === index }"
				@click="switchTab(index)"
			>
				<text>{{ tab.name }}</text>
			</view>
		</view>
		
		<!-- 消息列表 -->
		<view class="message-list">
			<view v-if="messages.length === 0" class="empty-state">
				<image src="/static/logo.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无消息</text>
			</view>
			
			<view v-else>
				<view 
					v-for="(item, index) in messages" 
					:key="index" 
					class="message-item"
					:class="{'unread': !item.read}"
					@click="showMessageDetail(item)"
				>
					<view class="avatar-container">
						<image :src="item.avatar || '/static/avatar.png'" class="avatar" mode="aspectFill"></image>
						<view v-if="!item.read" class="unread-dot"></view>
					</view>
					<view class="message-content">
						<view class="message-header">
							<text class="sender">{{ item.sender }}</text>
							<text class="time">{{ formatTime(item.time) }}</text>
						</view>
						<text class="preview">{{ item.content }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 消息详情弹窗 -->
		<uni-popup ref="messagePopup" type="center">
			<view class="message-detail">
				<view class="detail-header">
					<text class="detail-title">消息详情</text>
					<text class="close-btn" @click="closeMessageDetail">×</text>
				</view>
				<view class="detail-content">
					<view class="detail-info">
						<text class="detail-sender">{{ currentMessage.sender }}</text>
						<text class="detail-time">{{ formatTime(currentMessage.time) }}</text>
					</view>
					<text class="detail-text">{{ currentMessage.content }}</text>
				</view>
				<view class="detail-actions">
					<button class="action-btn delete" @click="deleteMessage">删除</button>
					<button class="action-btn mark" @click="markAsRead">标记为已读</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { http } from '@/api/request.js';
	
	export default {
		data() {
			return {
				messages: [],
				currentMessage: {},
				currentPage: 1,
				pageSize: 10,
				hasMore: true,
				loading: false,
				tabs: [
					{ name: '全部', type: 'all' },
					{ name: '系统通知', type: 'system' },
					{ name: '活动通知', type: 'activity' },
					{ name: '订单通知', type: 'order' }
				],
				currentTab: 0
			}
		},
		onLoad() {
			// 检查登录状态
			const app = getApp();
			if (!app.isLogin) {
				app.checkLogin();
				return;
			}
			
			// 加载消息列表
			this.loadMessages();
		},
		onPullDownRefresh() {
			// 下拉刷新
			this.currentPage = 1;
			this.hasMore = true;
			this.loadMessages(true).then(() => {
				uni.stopPullDownRefresh();
			});
		},
		onReachBottom() {
			// 上拉加载更多
			if (this.hasMore && !this.loading) {
				this.loadMoreMessages();
			}
		},
		methods: {
			// 切换标签
			switchTab(index) {
				this.currentTab = index;
				this.currentPage = 1;
				this.hasMore = true;
				this.messages = [];
				this.loadMessages();
			},
			
			// 加载消息列表
			async loadMessages(refresh = false) {
				try {
					this.loading = true;
					
					// 在实际应用中，这里应该调用API获取消息列表
					// const res = await http.get('message', {
					//     pageNum: this.currentPage,
					//     pageSize: this.pageSize,
					//     type: this.tabs[this.currentTab].type
					// });
					
					// 模拟API响应
					const mockResponse = this.getMockMessages();
					
					// 根据当前选中的标签过滤消息
					const filteredRecords = this.tabs[this.currentTab].type === 'all' 
						? mockResponse.records 
						: mockResponse.records.filter(msg => msg.type === this.tabs[this.currentTab].type);
					
					if (refresh) {
						this.messages = filteredRecords;
					} else {
						this.messages = [...this.messages, ...filteredRecords];
					}
					
					this.hasMore = this.currentPage < mockResponse.pages;
					this.loading = false;
				} catch (error) {
					console.error('加载消息失败', error);
					this.loading = false;
					uni.showToast({
						title: '加载消息失败',
						icon: 'none'
					});
				}
			},
			
			// 加载更多消息
			loadMoreMessages() {
				if (this.hasMore && !this.loading) {
					this.currentPage++;
					this.loadMessages();
				}
			},
			
			// 显示消息详情
			showMessageDetail(message) {
				this.currentMessage = { ...message };
				this.$refs.messagePopup.open();
				
				// 如果消息未读，标记为已读
				if (!message.read) {
					this.markMessageAsRead(message.id);
				}
			},
			
			// 关闭消息详情
			closeMessageDetail() {
				this.$refs.messagePopup.close();
			},
			
			// 删除消息
			deleteMessage() {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这条消息吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								// 在实际应用中，这里应该调用API删除消息
								// await http.delete(`message/${this.currentMessage.id}`);
								
								// 模拟删除消息
								this.messages = this.messages.filter(item => item.id !== this.currentMessage.id);
								
								this.closeMessageDetail();
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
							} catch (error) {
								console.error('删除消息失败', error);
								uni.showToast({
									title: '删除消息失败',
									icon: 'none'
								});
							}
						}
					}
				});
			},
			
			// 标记为已读
			markAsRead() {
				this.markMessageAsRead(this.currentMessage.id);
				this.closeMessageDetail();
			},
			
			// 标记消息为已读
			markMessageAsRead(id) {
				// 在实际应用中，这里应该调用API标记消息为已读
				// await http.put(`message/${id}/read`);
				
				// 模拟标记消息为已读
				const index = this.messages.findIndex(item => item.id === id);
				if (index !== -1) {
					this.messages[index].read = true;
					this.currentMessage.read = true;
				}
			},
			
			// 格式化时间
			formatTime(timestamp) {
				const date = new Date(timestamp);
				const now = new Date();
				
				// 今天的消息显示时间
				if (date.toDateString() === now.toDateString()) {
					return date.toTimeString().substring(0, 5);
				}
				
				// 昨天的消息显示"昨天"
				const yesterday = new Date(now);
				yesterday.setDate(now.getDate() - 1);
				if (date.toDateString() === yesterday.toDateString()) {
					return '昨天';
				}
				
				// 其他日期显示月日
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			},
			
			// 获取模拟消息数据
			getMockMessages() {
				const mockMessages = [
					{
						id: 1,
						sender: '系统通知',
						content: '欢迎使用SuakitsuApp！这是一个示例消息，用于展示消息功能。',
						time: Date.now() - 1000 * 60 * 5, // 5分钟前
						read: false,
						avatar: '/static/user/avatar.jpg',
						type: 'system'
					},
					{
						id: 2,
						sender: '客服小助手',
						content: '您好，有什么可以帮助您的吗？',
						time: Date.now() - 1000 * 60 * 60, // 1小时前
						read: true,
						avatar: '/static/user/avatar.jpg',
						type: 'system'
					},
					{
						id: 3,
						sender: '活动通知',
						content: '恭喜您获得新人专享优惠券一张，点击查看详情。',
						time: Date.now() - 1000 * 60 * 60 * 24, // 1天前
						read: false,
						avatar: '/static/user/avatar.jpg',
						type: 'activity'
					},
					{
						id: 4,
						sender: '系统通知',
						content: '您的账号已完成实名认证，感谢您的配合。',
						time: Date.now() - 1000 * 60 * 60 * 24 * 2, // 2天前
						read: true,
						avatar: '/static/user/avatar.jpg',
						type: 'system'
					},
					{
						id: 5,
						sender: '订单通知',
						content: '您的订单#12345已发货，预计3天内送达。',
						time: Date.now() - 1000 * 60 * 60 * 24 * 3, // 3天前
						read: true,
						avatar: '/static/user/avatar.jpg',
						type: 'order'
					}
				];
				
				return {
					total: mockMessages.length,
					pages: 1,
					records: mockMessages
				};
			}
		}
	}
</script>

<style>
	.message-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f8f8f8;
	}
	
	.header {
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		border-bottom: 1rpx solid #eeeeee;
		position: sticky;
		top: 0;
		z-index: 100;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.message-list {
		flex: 1;
		padding: 20rpx;
	}
	
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 600rpx;
	}
	
	.empty-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.message-item {
		display: flex;
		padding: 30rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.message-item.unread {
		background-color: #f0f9ff;
	}
	
	.avatar-container {
		position: relative;
		margin-right: 20rpx;
	}
	
	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
	}
	
	.unread-dot {
		position: absolute;
		top: 0;
		right: 0;
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		background-color: #ff4d4f;
	}
	
	.message-content {
		flex: 1;
	}
	
	.message-header {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
	}
	
	.sender {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.time {
		font-size: 24rpx;
		color: #999999;
	}
	
	.preview {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	
	.tabs {
		display: flex;
		background-color: #ffffff;
		padding: 0 20rpx;
		border-bottom: 1rpx solid #eeeeee;
		overflow-x: auto;
		white-space: nowrap;
	}
	
	.tab-item {
		padding: 20rpx 30rpx;
		position: relative;
	}
	
	.tab-item text {
		font-size: 28rpx;
		color: #666666;
	}
	
	.tab-item.active text {
		color: #1890ff;
		font-weight: 500;
	}
	
	.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #1890ff;
		border-radius: 2rpx;
	}
	.message-detail {
		width: 600rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
	}
	
	.detail-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #eeeeee;
	}
	
	.detail-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.close-btn {
		font-size: 40rpx;
		color: #999999;
		padding: 0 20rpx;
	}
	
	.detail-content {
		padding: 30rpx;
		max-height: 600rpx;
		overflow-y: auto;
	}
	
	.detail-info {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
	}
	
	.detail-sender {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.detail-time {
		font-size: 24rpx;
		color: #999999;
	}
	
	.detail-text {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.6;
	}
	
	.detail-actions {
		display: flex;
		justify-content: space-between;
		padding: 30rpx;
		border-top: 1rpx solid #eeeeee;
	}
	
	.action-btn {
		width: 45%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		font-size: 28rpx;
	}
	
	.delete {
		background-color: #ff4d4f;
		color: #ffffff;
	}
	
	.mark {
		background-color: #1890ff;
		color: #ffffff;
	}
</style>
	